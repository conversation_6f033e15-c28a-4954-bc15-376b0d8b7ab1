import express, { Request, Response, NextFunction } from 'express';
import cors from 'cors';
import { createServer } from 'http';
import { Server as SocketIOServer } from 'socket.io';
import { v4 as uuidv4 } from 'uuid';
import { ProjectContext, SavedProjectEntry, AgentLog, Task } from '../types.js';
import { ProjectGenerationService } from './services/ProjectGenerationService.js';
import { BackendGeminiService } from './services/BackendGeminiService.js';

const app = express();
const server = createServer(app);
const io = new SocketIOServer(server, {
  cors: {
    origin: "http://localhost:5173", // Vite dev server
    methods: ["GET", "POST"]
  }
});
const port = process.env.BACKEND_PORT || 3002;

// Initialize project generation service
const projectGenerationService = new ProjectGenerationService(io);

app.use(cors());
app.use(express.json({ limit: '50mb' })); // Allow larger payloads for ProjectContext

// Request validation middleware
const validateProjectData = (req: Request, res: Response, next: NextFunction) => {
  if (req.method === 'POST' && req.path === '/api/projects') {
    const { body } = req;
    if (!body || typeof body !== 'object') {
      return res.status(400).json({ error: 'Request body is required and must be an object' });
    }

    if (!body.name || typeof body.name !== 'string' || body.name.trim().length === 0) {
      return res.status(400).json({ error: 'Project name is required and must be a non-empty string' });
    }

    if (!body.idea || typeof body.idea !== 'string' || body.idea.trim().length === 0) {
      return res.status(400).json({ error: 'Project idea is required and must be a non-empty string' });
    }
  }

  if (req.method === 'PUT' && req.path.startsWith('/api/projects/')) {
    const { body } = req;
    if (!body || typeof body !== 'object') {
      return res.status(400).json({ error: 'Request body is required and must be an object' });
    }
  }

  next();
};

// Apply validation middleware to project routes
app.use('/api/projects', validateProjectData);

// In-memory store
let projectsStore: Record<string, ProjectContext> = {};
let companyLogsStore: AgentLog[] = [];

// WebSocket connection handling
io.on('connection', (socket) => {
  console.log(`[WebSocket] Client connected: ${socket.id}`);

  socket.on('disconnect', () => {
    console.log(`[WebSocket] Client disconnected: ${socket.id}`);
  });

  socket.on('join-project', (projectId: string) => {
    socket.join(`project-${projectId}`);
    console.log(`[WebSocket] Client ${socket.id} joined project ${projectId}`);
  });

  socket.on('leave-project', (projectId: string) => {
    socket.leave(`project-${projectId}`);
    console.log(`[WebSocket] Client ${socket.id} left project ${projectId}`);
  });
});

// Helper to create SavedProjectEntry from ProjectContext
const createSavedEntry = (project: ProjectContext): SavedProjectEntry => ({
  id: project.id,
  name: project.name,
  lastModified: project.lastModified,
  ideaSnippet: project.idea.substring(0, 100) + (project.idea.length > 100 ? '...' : ''),
  taskCount: project.tasks.length,
  completedTaskCount: project.tasks.filter((t: Task) => t.status === 'completed').length,
});

// --- Project Endpoints ---
app.get('/api/projects', (_req: Request, res: Response) => {
  const savedEntries = Object.values(projectsStore)
    .map(createSavedEntry)
    .sort((a, b) => new Date(b.lastModified).getTime() - new Date(a.lastModified).getTime());
  res.json(savedEntries);
});

app.post('/api/projects', (req: Request, res: Response) => {
  try {
    const project = req.body as ProjectContext;
    if (!project.id) {
      project.id = uuidv4(); // Assign an ID if not present
    }
    project.lastModified = new Date().toISOString();
    projectsStore[project.id] = project;
    console.log(`[Backend] Project created/received: ${project.id} - ${project.name}`);
    res.status(201).json({ id: project.id, name: project.name });
  } catch (error) {
    console.error('[Backend] Error creating project:', error);
    res.status(500).json({ error: 'Failed to create project' });
  }
});

app.get('/api/projects/:id', (req: Request, res: Response) => {
  const project = projectsStore[req.params.id];
  if (project) {
    res.json(project);
  } else {
    res.status(404).json({ message: 'Project not found' });
  }
});

app.put('/api/projects/:id', (req: Request, res: Response) => {
  try {
    const projectId = req.params.id;
    if (!projectId || typeof projectId !== 'string') {
      return res.status(400).json({ error: 'Valid project ID is required' });
    }

    if (!projectsStore[projectId]) {
      return res.status(404).json({ error: 'Project not found for update. Use POST to create.' });
    }

    const updatedProject = req.body as ProjectContext;
    updatedProject.id = projectId; // Ensure ID matches
    updatedProject.lastModified = new Date().toISOString();
    projectsStore[projectId] = updatedProject;
    console.log(`[Backend] Project updated: ${projectId} - ${updatedProject.name}`);
    res.json(updatedProject);
  } catch (error) {
    console.error('[Backend] Error updating project:', error);
    res.status(500).json({ error: 'Failed to update project' });
  }
});

app.delete('/api/projects/:id', (req: Request, res: Response) => {
  const projectId = req.params.id;
  if (projectsStore[projectId]) {
    const projectName = projectsStore[projectId].name;
    delete projectsStore[projectId];
    console.log(`[Backend] Project deleted: ${projectId} - ${projectName}`);
    res.status(204).send();
  } else {
    res.status(404).json({ message: 'Project not found' });
  }
});

// --- Log Endpoint ---
app.post('/api/logs', (req: Request, res: Response) => {
  try {
    const logEntry = req.body as AgentLog;

    // Enhanced validation for log entries
    if (!logEntry || typeof logEntry !== 'object') {
      return res.status(400).json({ error: 'Log entry must be an object' });
    }

    if (!logEntry.id || typeof logEntry.id !== 'string') {
      return res.status(400).json({ error: 'Log entry must have a valid id' });
    }

    if (!logEntry.message || typeof logEntry.message !== 'string') {
      return res.status(400).json({ error: 'Log entry must have a valid message' });
    }

    if (!logEntry.agent || typeof logEntry.agent !== 'string') {
      return res.status(400).json({ error: 'Log entry must have a valid agent' });
    }

    companyLogsStore.push(logEntry);
    if (companyLogsStore.length > 5000) { // Keep last 5000 logs
        companyLogsStore = companyLogsStore.slice(-5000);
    }
    // console.log(`[Backend] Log received: ${logEntry.agent} - ${logEntry.message.substring(0,50)}...`);
    res.status(201).json({ message: 'Log received' });
  } catch (error) {
    console.error('[Backend] Error processing log:', error);
    res.status(500).json({ error: 'Failed to process log entry' });
  }
});

// --- Project Generation Endpoints ---

/**
 * Initialize project generation service with API key
 */
app.post('/api/generation/initialize', (req: Request, res: Response) => {
  try {
    const { apiKey } = req.body;
    if (!apiKey || typeof apiKey !== 'string') {
      return res.status(400).json({ error: 'API key is required' });
    }

    projectGenerationService.initializeWithApiKey(apiKey);
    console.log('[Backend] Project generation service initialized with API key');
    res.json({ message: 'Project generation service initialized successfully' });
  } catch (error) {
    console.error('[Backend] Error initializing project generation service:', error);
    res.status(500).json({ error: 'Failed to initialize project generation service' });
  }
});

/**
 * Start project generation workflow
 */
app.post('/api/generation/start', async (req: Request, res: Response) => {
  try {
    const { projectId, projectIdea, licenseInfo, agentModelConfiguration } = req.body;

    if (!projectId || !projectIdea || !licenseInfo || !agentModelConfiguration) {
      return res.status(400).json({ error: 'Missing required fields: projectId, projectIdea, licenseInfo, agentModelConfiguration' });
    }

    // Check if generation is already in progress
    const status = projectGenerationService.getGenerationStatus();
    if (status.isGenerating) {
      return res.status(409).json({ error: 'Project generation already in progress', currentProjectId: status.projectId });
    }

    // Start generation in background
    projectGenerationService.startProjectGeneration(
      projectId,
      projectIdea,
      licenseInfo,
      agentModelConfiguration
    ).catch(error => {
      console.error('[Backend] Project generation failed:', error);
      // Error will be emitted via WebSocket
    });

    console.log(`[Backend] Started project generation for: ${projectId}`);
    res.json({ message: 'Project generation started', projectId });
  } catch (error) {
    console.error('[Backend] Error starting project generation:', error);
    res.status(500).json({ error: 'Failed to start project generation' });
  }
});

/**
 * Get project generation status
 */
app.get('/api/generation/status', (req: Request, res: Response) => {
  try {
    const status = projectGenerationService.getGenerationStatus();
    res.json(status);
  } catch (error) {
    console.error('[Backend] Error getting generation status:', error);
    res.status(500).json({ error: 'Failed to get generation status' });
  }
});

/**
 * Process user feedback and generate additional tasks
 */
app.post('/api/generation/feedback', async (req: Request, res: Response) => {
  try {
    const { projectId, projectContext, projectIdea, fileStructure, feedback, modelName } = req.body;

    if (!projectId || !projectContext || !projectIdea || !fileStructure || !feedback || !modelName) {
      return res.status(400).json({ error: 'Missing required fields' });
    }

    const newTasks = await projectGenerationService.processUserFeedback(
      projectId,
      projectContext,
      projectIdea,
      fileStructure,
      feedback,
      modelName
    );

    console.log(`[Backend] Generated ${newTasks.length} tasks from user feedback for project: ${projectId}`);
    res.json({ tasks: newTasks });
  } catch (error) {
    console.error('[Backend] Error processing user feedback:', error);
    res.status(500).json({ error: 'Failed to process user feedback' });
  }
});

// Basic error handler
app.use((err: Error, _req: Request, res: Response, _next: NextFunction) => {
  console.error("[Backend Error]", err.stack);
  res.status(500).send('Something broke on the backend!');
});

server.listen(port, () => {
  console.log(`[Backend] DevGenius Studio Backend listening on port ${port}`);
  console.log(`[Backend] WebSocket server enabled for real-time updates`);
  console.log(`[Backend] Available Endpoints:
  GET    /api/projects
  POST   /api/projects
  GET    /api/projects/:id
  PUT    /api/projects/:id
  DELETE /api/projects/:id
  POST   /api/logs
  POST   /api/generation/initialize
  POST   /api/generation/start
  GET    /api/generation/status
  POST   /api/generation/feedback`);
});
