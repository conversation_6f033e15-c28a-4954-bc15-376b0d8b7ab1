import { v4 as uuidv4 } from 'uuid';
import { AgentType } from '../../types.js';
import { BackendGeminiService } from './BackendGeminiService.js';
import { BackendPlannerAgent } from '../agents/BackendPlannerAgent.js';
import { BackendCoderAgent } from '../agents/BackendCoderAgent.js';
import { BackendContextManagerAgent } from '../agents/BackendContextManagerAgent.js';
/**
 * Service that handles the complete project generation workflow on the backend
 * This replaces the frontend logic that was causing memory issues
 */
export class ProjectGenerationService {
    constructor(io) {
        this.geminiService = null;
        this.plannerAgent = null;
        this.coderAgent = null;
        this.contextManagerAgent = null;
        this.isGenerating = false;
        this.currentProjectId = null;
        this.io = io;
    }
    /**
     * Initialize the service with API key and create agents
     */
    initializeWithApiKey(apiKey) {
        this.geminiService = new BackendGeminiService(apiKey);
        const loggingInterface = {
            addCompanyLog: this.addCompanyLog.bind(this),
            addTaskLog: this.addTaskLog.bind(this),
            addDecisionLogEntry: this.addDecisionLogEntry.bind(this)
        };
        this.plannerAgent = new BackendPlannerAgent(this.geminiService, loggingInterface);
        this.coderAgent = new BackendCoderAgent(this.geminiService, loggingInterface);
        this.contextManagerAgent = new BackendContextManagerAgent(this.geminiService, loggingInterface);
    }
    /**
     * Start project generation workflow
     */
    async startProjectGeneration(projectId, projectIdea, licenseInfo, agentModelConfiguration) {
        if (this.isGenerating) {
            throw new Error('Project generation already in progress');
        }
        if (!this.geminiService || !this.plannerAgent) {
            throw new Error('Service not initialized with API key');
        }
        this.isGenerating = true;
        this.currentProjectId = projectId;
        try {
            await this.addCompanyLog('System', `Starting project generation for: ${projectIdea}`, 'working');
            // Phase 1: Generate initial plan
            await this.addCompanyLog('Planner Agent', 'Generating initial project plan...', 'working');
            const plannerModel = agentModelConfiguration[AgentType.PLANNER];
            const projectContext = `Project ID: ${projectId}\nProject Idea: ${projectIdea}\nLicense: ${licenseInfo.type}`;
            const initialPlan = await this.plannerAgent.generatePlan(projectIdea, plannerModel, projectContext, licenseInfo);
            await this.addCompanyLog('Planner Agent', `Initial plan generated with ${initialPlan.tasks.length} tasks`, 'success');
            await this.addDecisionLogEntry('Planner Agent', 'Initial Plan Generated', `Created ${initialPlan.tasks.length} tasks and ${initialPlan.fileStructure?.length || 0} file structure items`);
            // Emit initial plan to frontend
            this.io.emit('project-generation-update', {
                projectId,
                phase: 'planning',
                data: {
                    tasks: initialPlan.tasks,
                    fileStructure: initialPlan.fileStructure,
                    technologyStackSuggestion: initialPlan.technologyStackSuggestion
                }
            });
            // Phase 2: Review and refine plan
            await this.addCompanyLog('Planner Agent', 'Reviewing and refining initial plan...', 'working');
            const refinedPlan = await this.plannerAgent.reviewAndRefinePlan(projectIdea, initialPlan.tasks, initialPlan.fileStructure || [], projectContext, plannerModel, initialPlan.technologyStackSuggestion);
            await this.addCompanyLog('Planner Agent', 'Plan reviewed and finalized', 'success');
            await this.addDecisionLogEntry('Planner Agent', 'Plan Reviewed & Finalized', `Final plan: ${refinedPlan.tasks.length} tasks, ${refinedPlan.fileStructure?.length || 0} files`);
            // Convert to full Task objects
            const finalTasks = refinedPlan.tasks.map(t => ({
                ...t,
                id: t.id || uuidv4(),
                status: 'pending',
                currentProcessingStage: 'QUEUED',
                agentMessages: [],
                identifiedBugs: [],
                unresolvedBugs: [],
                bugFixingCycles: 0
            }));
            // Convert to full FileNode objects
            const finalFileStructure = (refinedPlan.fileStructure || []).map(n => ({
                ...n,
                id: uuidv4(),
                path: n.name,
                children: n.children?.map(c => ({
                    ...c,
                    id: uuidv4(),
                    path: `${n.name}/${c.name}`,
                    children: []
                })) || []
            }));
            // Emit final plan to frontend
            this.io.emit('project-generation-update', {
                projectId,
                phase: 'plan-finalized',
                data: {
                    tasks: finalTasks,
                    fileStructure: finalFileStructure,
                    technologyStackSuggestion: refinedPlan.technologyStackSuggestion,
                    reviewNotes: refinedPlan.reviewNotes
                }
            });
            await this.addCompanyLog('System', 'Project generation completed successfully', 'success');
        }
        catch (error) {
            const errorMessage = error instanceof Error ? error.message : String(error);
            await this.addCompanyLog('System', `Project generation failed: ${errorMessage}`, 'error');
            this.io.emit('project-generation-error', {
                projectId,
                error: errorMessage
            });
            throw error;
        }
        finally {
            this.isGenerating = false;
            this.currentProjectId = null;
        }
    }
    /**
     * Process user feedback and generate additional tasks
     */
    async processUserFeedback(projectId, projectContext, projectIdea, fileStructure, feedback, modelName) {
        if (!this.plannerAgent) {
            throw new Error('Service not initialized');
        }
        await this.addCompanyLog('Planner Agent', `Processing user feedback: ${feedback.description.substring(0, 50)}...`, 'working');
        const feedbackResponse = await this.plannerAgent.generateTasksFromFeedback(projectContext, projectIdea, fileStructure, feedback, modelName);
        const newTasks = feedbackResponse.tasks.map(t => ({
            ...t,
            id: t.id || uuidv4(),
            status: 'pending',
            currentProcessingStage: 'QUEUED',
            agentMessages: [],
            identifiedBugs: [],
            unresolvedBugs: [],
            bugFixingCycles: 0,
            purpose: 'user-feedback-driven'
        }));
        await this.addCompanyLog('Planner Agent', `Generated ${newTasks.length} tasks from user feedback`, 'success');
        await this.addDecisionLogEntry('Planner Agent', 'Tasks Generated from User Feedback', `Generated ${newTasks.length} tasks to address user feedback`);
        return newTasks;
    }
    /**
     * Get current generation status
     */
    getGenerationStatus() {
        return {
            isGenerating: this.isGenerating,
            projectId: this.currentProjectId
        };
    }
    /**
     * Add company log and emit to frontend
     */
    async addCompanyLog(agent, message, status, taskId) {
        const logEntry = {
            id: uuidv4(),
            timestamp: new Date(),
            agent,
            message,
            status
        };
        this.io.emit('company-log', logEntry);
        console.log(`[${agent}] ${message}`);
    }
    /**
     * Add task log and emit to frontend
     */
    async addTaskLog(taskId, agent, message, status, stage, attachments) {
        const logEntry = {
            taskId,
            agent,
            message,
            status,
            stage,
            attachments,
            timestamp: new Date()
        };
        this.io.emit('task-log', logEntry);
        console.log(`[${agent}] Task ${taskId}: ${message}`);
    }
    /**
     * Add decision log entry and emit to frontend
     */
    async addDecisionLogEntry(agent, decision, reasoning, stackTrace, taskId) {
        // Map string agent names to proper types
        let agentType;
        switch (agent) {
            case 'Planner Agent':
                agentType = AgentType.PLANNER;
                break;
            case 'Coder Agent':
                agentType = AgentType.CODER;
                break;
            case 'Context Manager Agent':
                agentType = AgentType.CONTEXT_MANAGER;
                break;
            case 'System':
                agentType = 'System';
                break;
            default:
                agentType = 'System'; // Default fallback
        }
        const entry = {
            id: uuidv4(),
            timestamp: new Date(),
            agent: agentType,
            action: decision,
            details: reasoning,
            reason: stackTrace,
            taskId
        };
        this.io.emit('decision-log', entry);
        console.log(`[${agent}] Decision: ${decision} - ${reasoning}`);
    }
}
//# sourceMappingURL=ProjectGenerationService.js.map