
import { GeminiService } from '../services/geminiService';
import { GeminiJsonPlannerResponse, UserFeedback, FileNode, LicenseInfo, GeminiJsonPlannerTask, UnifiedLoggingInterface, AgentType } from '../types';

/**
 * PlannerAgent handles project planning, task generation, and plan refinement.
 * Provides comprehensive logging of all planning activities and decisions.
 */
export class PlannerAgent {
  private geminiService: GeminiService;
  private logger: UnifiedLoggingInterface;

  constructor(geminiService: GeminiService, logger: UnifiedLoggingInterface) {
    if (!geminiService) {
      throw new Error("PlannerAgent: GeminiService instance is required.");
    }
    if (!logger) {
      throw new Error("PlannerAgent: UnifiedLoggingInterface instance is required.");
    }
    this.geminiService = geminiService;
    this.logger = logger;
  }

  /**
   * Logs company-level activities for this agent
   */
  private async logActivity(message: string, status: 'info' | 'success' | 'error' | 'working', taskId?: string): Promise<void> {
    await this.logger.addCompanyLog('Planner Agent', message, status, taskId);
  }

  /**
   * Logs decision-making activities for this agent
   */
  private logDecision(action: string, details: string, reason?: string, taskId?: string): void {
    this.logger.addDecisionLogEntry?.(AgentType.PLANNER, action, details, reason, taskId);
  }

  /**
   * Generates the initial project plan, including tasks and file structure.
   * @param projectIdea - The user's idea for the project.
   * @param modelName - The name of the Gemini model to use.
   * @param projectContext - The current full context of the project.
   * @param licenseInfo - The chosen license information for the project.
   * @returns A promise that resolves to the planner's response.
   */
  public async generatePlan(
    projectIdea: string,
    modelName: string,
    projectContext: string, // Added project context
    licenseInfo?: LicenseInfo    // Added license info
  ): Promise<GeminiJsonPlannerResponse> {
    try {
      await this.logActivity(`Starting initial plan generation for project idea: "${projectIdea.substring(0, 50)}${projectIdea.length > 50 ? '...' : ''}"`, 'working');
      this.logDecision('Plan Generation Started', `Using model: ${modelName}. License: ${licenseInfo?.type || 'Unspecified'}`, 'Initial project planning phase');

      // Pass projectContext and licenseInfo to the service method
      const plan = await this.geminiService.generateInitialPlan(projectIdea, modelName, projectContext, licenseInfo);

      await this.logActivity(`Initial plan generated successfully. Tasks: ${plan.tasks.length}, Files: ${plan.fileStructure?.length || 0}`, 'success');
      this.logDecision('Plan Generated', `Created ${plan.tasks.length} tasks and ${plan.fileStructure?.length || 0} file structure items`, 'Plan generation completed successfully');

      return plan;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      await this.logActivity(`Failed to generate initial plan: ${errorMessage}`, 'error');
      this.logDecision('Plan Generation Failed', `Error: ${errorMessage}`, 'Plan generation encountered an error');
      console.error("PlannerAgent: Error generating plan -", error);
      throw error;
    }
  }

  /**
   * Generates tasks based on user feedback.
   * @param projectContext - The current full context of the project.
   * @param projectIdea - The original project idea.
   * @param fileStructure - The current file structure.
   * @param feedback - The user's feedback.
   * @param modelName - The name of the Gemini model to use.
   * @returns A promise that resolves to a planner response containing new tasks.
   */
  public async generateTasksFromFeedback(
    projectContext: string,
    projectIdea: string,
    fileStructure: FileNode[],
    feedback: UserFeedback,
    modelName: string
  ): Promise<GeminiJsonPlannerResponse> {
    try {
      await this.logActivity(`Processing user feedback: ${feedback.type} - "${feedback.description.substring(0, 50)}${feedback.description.length > 50 ? '...' : ''}"`, 'working');
      this.logDecision('Feedback Processing Started', `Type: ${feedback.type}, File: ${feedback.filePath || 'N/A'}`, 'User provided feedback requiring task generation');

      const response = await this.geminiService.generateTasksFromUserFeedback(
        projectContext,
        projectIdea,
        fileStructure,
        feedback,
        modelName
      );

      await this.logActivity(`Generated ${response.tasks.length} tasks from user feedback`, 'success');
      this.logDecision('Feedback Tasks Generated', `Created ${response.tasks.length} new tasks based on user feedback`, 'Feedback processing completed');

      // The response from generateTasksFromUserFeedback might only contain tasks.
      // Ensure it fits the GeminiJsonPlannerResponse structure, fileStructure might be undefined.
      return {
        tasks: response.tasks,
        fileStructure: response.fileStructure // This will likely be undefined as feedback planner focuses on tasks
      };
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      await this.logActivity(`Failed to generate tasks from feedback: ${errorMessage}`, 'error');
      this.logDecision('Feedback Processing Failed', `Error: ${errorMessage}`, 'Feedback task generation encountered an error');
      console.error("PlannerAgent: Error generating tasks from feedback -", error);
      throw error;
    }
  }

  /**
   * Reviews and refines an initial project plan.
   * @param projectIdea - The original project idea.
   * @param initialTasks - The initial tasks to review.
   * @param initialFileStructure - The initial file structure to review.
   * @param projectContext - The current project context.
   * @param modelName - The name of the Gemini model to use.
   * @param technologyStackSuggestion - Optional technology stack suggestion.
   * @returns A promise that resolves to the refined plan response.
   */
  public async reviewAndRefinePlan(
    projectIdea: string,
    initialTasks: GeminiJsonPlannerTask[],
    initialFileStructure: Array<Omit<FileNode, 'id' | 'path' | 'children' | 'isTestFile'> & { children?: Array<Omit<FileNode, 'id' | 'path' | 'isTestFile'>> }>,
    projectContext: string,
    modelName: string,
    technologyStackSuggestion?: string
  ): Promise<GeminiJsonPlannerResponse> {
    try {
      await this.logActivity(`Reviewing and refining initial plan. Initial tasks: ${initialTasks.length}, Files: ${initialFileStructure.length}`, 'working');
      this.logDecision('Plan Review Started', `Reviewing ${initialTasks.length} tasks and ${initialFileStructure.length} files. Tech stack: ${technologyStackSuggestion || 'Not specified'}`, 'Plan refinement phase initiated');

      const response = await this.geminiService.reviewAndRefinePlan(
        projectIdea,
        initialTasks,
        initialFileStructure,
        projectContext,
        modelName,
        technologyStackSuggestion
      );

      await this.logActivity(`Plan review completed. Final tasks: ${response.tasks.length}, Files: ${response.fileStructure?.length || 0}`, 'success');
      this.logDecision('Plan Review Completed', `Refined to ${response.tasks.length} tasks and ${response.fileStructure?.length || 0} files`, 'Plan refinement completed successfully');

      return response;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      await this.logActivity(`Failed to review and refine plan: ${errorMessage}`, 'error');
      this.logDecision('Plan Review Failed', `Error: ${errorMessage}`, 'Plan review encountered an error');
      console.error("PlannerAgent: Error reviewing and refining plan -", error);
      throw error;
    }
  }

  /**
   * Validates the project build configuration and identifies potential issues.
   * @param projectContext - The current project context.
   * @param fileStructure - The current file structure of the project.
   * @param packageJsonContent - The content of package.json file if available.
   * @param modelName - The name of the Gemini model to use.
   * @returns A promise that resolves to the build validation response.
   */
  public async validateProjectBuild(
    projectContext: string,
    fileStructure: FileNode[],
    packageJsonContent: string | undefined,
    modelName: string
  ): Promise<any> {
    try {
      await this.logActivity(`Validating project build configuration. Files: ${fileStructure.length}, Package.json: ${packageJsonContent ? 'Present' : 'Missing'}`, 'working');
      this.logDecision('Build Validation Started', `Analyzing ${fileStructure.length} files for build configuration issues`, 'Build validation phase initiated');

      const response = await this.geminiService.validateProjectBuild(
        projectContext,
        fileStructure,
        packageJsonContent,
        modelName
      );

      await this.logActivity(`Build validation completed successfully`, 'success');
      this.logDecision('Build Validation Completed', `Build configuration analysis finished`, 'Build validation completed without errors');

      return response;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      await this.logActivity(`Failed to validate project build: ${errorMessage}`, 'error');
      this.logDecision('Build Validation Failed', `Error: ${errorMessage}`, 'Build validation encountered an error');
      console.error("PlannerAgent: Error validating project build -", error);
      throw error;
    }
  }
}
